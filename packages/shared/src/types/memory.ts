import { z } from 'zod';
import { MemoryItem } from './api';
import { MessageSchema } from './api';

export interface MemoryRelation {
  source: string;
  relationship: string;
  destination: string;
}

export interface SearchFilters {
  userId?: string;
  conversationId?: string;
  hostname?: string;
  faqId?: string;
}

export interface AddMemoryOptions {
  metadata?: Record<string, any>;
  filters?: SearchFilters;
}

export interface SearchResult {
  results: MemoryItem[];
  relations?: any[];
}

export const SearchFiltersSchema = z.object({
  userId: z.string().optional(),
  conversationId: z.string().optional(),
  hostname: z.string().optional(),
  faqId: z.string().optional(),
});

export const SearchMemoryOptionsSchema = z.object({
  limit: z.number(),
  filters: SearchFiltersSchema,
});

export const AddMemoryOptionsSchema = z.object({
  metadata: z.record(z.any()).optional(),
  filters: SearchFiltersSchema,
});

export type SearchMemoryOptions = z.infer<typeof SearchMemoryOptionsSchema>;

export const SearchMemoryRequestSchemaV2 = z.object({
  text: z.string(),
  config: SearchMemoryOptionsSchema,
});

/**
 * api-cf request memory-server
 */
export type SearchMemoryRequestV2 = z.infer<typeof SearchMemoryRequestSchemaV2>;

export const AddSiteMessageRequestSchema = z.object({
  messages: z.array(MessageSchema),
  config: AddMemoryOptionsSchema,
});

export type AddSiteMessageRequest = z.infer<typeof AddSiteMessageRequestSchema>;

export const MemoryTypeSchema = z.enum(['procedural', 'semantic', 'episodic']);
export type MemoryType = z.infer<typeof MemoryTypeSchema>;

export const EntitySchema = z.object({
  userId: z.string().optional(),
  agentId: z.string().optional(),
  runId: z.string().optional(),
  taskId: z.string().optional(),
  workflowId: z.string().optional(),
  faqId: z.string().optional(),
  conversationId: z.string().optional(),
  hostname: z.string().optional(),
  memoryType: MemoryTypeSchema.optional(),
});

export type Entity = z.infer<typeof EntitySchema>;

export const MemoryMetadataSchema = z
  .object({
    created_at: z.string().optional(),
    updated_at: z.string().optional(),
  })
  .passthrough();
export type MemoryMetadata = Entity & z.infer<typeof MemoryMetadataSchema>;

// Memory Config Schema
export const MemoryConfigSchema = z.object({
  filters: EntitySchema.optional(),
  metadata: z.union([MemoryMetadataSchema, z.record(z.unknown())]).optional(),
});
export type MemoryConfig = z.infer<typeof MemoryConfigSchema>;

// Execution Step Types
export const ExecutionStepSchema = z.object({
  stepNumber: z.number(),
  agentAction: z.string(),
  actionResult: z.string(),
  keyFindings: z.string().optional(),
  navigationHistory: z.string().optional(),
  errors: z.string().optional(),
  currentContext: z.string(),
  timestamp: z.string(),
});
export type ExecutionStep = z.infer<typeof ExecutionStepSchema>;

// Add Memory Request Schema
export const AddMemoryRequestSchema = z.object({
  messages: z.array(MessageSchema),
  config: MemoryConfigSchema.optional(),
});
export type AddMemoryRequest = z.infer<typeof AddMemoryRequestSchema>;
