import { OpenAPIRoute } from 'chanfana';
import { Context } from 'hono';
import { GatewayServiceError } from '../types/service';
import { getUserBalance } from '../d1/user';
import { deductUserCredits } from '../d1/user';
import { EMBEDDING_MODEL } from '../utils/common';
import { addMessagesToMemory, collectText, searchMemory } from '../utils/memory';
import {
  Message,
  AddMemoryRequestSchema,
  SearchMemoryRequestSchema,
  SearchMemoryResponseSchema,
  AddMemoryResponseSchema,
  AddMemoryOptions,
  MemoryConfig,
  Entity,
  MemoryType,
} from '@the-agent/shared';

export class SearchMemory extends OpenAPIRoute {
  schema = {
    request: {
      query: SearchMemoryRequestSchema,
    },
    responses: {
      '200': {
        description: 'Search memory successfully',
        content: {
          'application/json': {
            schema: SearchMemoryResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    if (!userId) {
      throw new GatewayServiceError(401, 'Unauthorized');
    }

    const currentCredits = await getUserBalance(c.env, userId);
    if (currentCredits <= 0) {
      throw new GatewayServiceError(402, 'Insufficient credits');
    }

    // get all query parameters
    const query = c.req.query();
    const { text, limit, ...entityParams } = query;

    if (!text) {
      throw new GatewayServiceError(400, 'Invalid request: text is required');
    }

    // build search filters with flexible entity parameter handling
    const filters: Entity = { userId };

    // dynamically add all valid entity parameters with type safety
    const entityParamKeys: (keyof Entity)[] = [
      'conversationId',
      'agentId',
      'runId',
      'taskId',
      'workflowId',
      'faqId',
      'hostname',
      'memoryType',
    ];

    for (const key of entityParamKeys) {
      if (entityParams[key]) {
        if (key === 'memoryType') {
          filters[key] = entityParams[key] as MemoryType;
        } else {
          filters[key] = entityParams[key] as string;
        }
      }
    }

    const result = await searchMemory(c.env, text, {
      limit: parseInt(limit ?? '3'),
      filters,
    });

    // - 50 credit overhead
    // - 100 credit for supabase API cost
    // - embedding generation: 10000 per 1M tokens, 100 token per credit
    const totalCost = 150 + text.length / 100;
    await deductUserCredits(c.env, userId, totalCost, EMBEDDING_MODEL);

    // Return success response with CORS headers
    return c.json(
      {
        results: result.results,
        relations: result.relations,
      },
      200
    );
  }
}

export class AddMemory extends OpenAPIRoute {
  schema = {
    request: {
      body: {
        content: {
          'application/json': {
            schema: AddMemoryRequestSchema,
          },
        },
      },
    },
    responses: {
      '200': {
        description: 'Add memory successfully',
        content: {
          'application/json': {
            schema: AddMemoryResponseSchema,
          },
        },
      },
    },
  };

  async handle(c: Context) {
    const userId = c.get('userId');
    const body = await c.req.json();

    let request: { messages: Message[]; config?: MemoryConfig };
    try {
      request = AddMemoryRequestSchema.parse(body);
    } catch (error) {
      console.error('Invalid message format:', error);
      throw new GatewayServiceError(400, 'Invalid message format');
    }

    const currentCredits = await getUserBalance(c.env, userId);
    if (currentCredits <= 0) {
      throw new GatewayServiceError(402, 'Insufficient credits');
    }

    if (!request.messages) {
      throw new GatewayServiceError(400, 'Invalid request');
    }

    const messages: Message[] = request.messages.filter(message => {
      const content = collectText(message);
      if (content.length > 0) {
        message.content = content;
        return message;
      }
    });

    const config: AddMemoryOptions = {
      metadata: {
        ...request.config?.metadata,
        userId,
      },
      filters: {
        ...request.config?.filters,
        userId,
      },
    };

    const result = await addMessagesToMemory(c.env, messages, config);

    const totalTextLength = messages.reduce((acc, p) => acc + (p.content?.length ?? 0), 0);
    const totalMessages = messages.length;
    // - 50 credit overhead
    // - 100 credit for supabase API cost
    // - embedding generation: 10000 per 1M tokens, 100 token per credit
    const totalCost = 150 * totalMessages + totalTextLength / 100;
    await deductUserCredits(c.env, userId, totalCost, EMBEDDING_MODEL);

    // Return success response with CORS headers
    return c.json(result, 200);
  }
}
