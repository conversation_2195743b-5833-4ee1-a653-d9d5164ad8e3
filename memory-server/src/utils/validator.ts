import {
  AddMemoryRequest,
  AddSiteMessageRequest,
  MemoryConfig,
  MemoryMetadata,
  SearchMemoryRequestV2,
} from '@the-agent/shared';

export interface ValidationResult {
  isValid: boolean;
  error?: string;
}

export class MemoryValidator {
  /**
   * Validate add memory request
   */
  static validateAddMemoryRequest(request: AddMemoryRequest): ValidationResult {
    if (!request.messages || !Array.isArray(request.messages) || request.messages.length === 0) {
      return { isValid: false, error: 'messages array is required and must not be empty' };
    }

    if (!request.config?.filters?.userId) {
      return { isValid: false, error: 'userId is required in add memory request' };
    }

    if (request.messages.some(message => !message.conversation_id)) {
      return { isValid: false, error: 'conversation_id is required in messages' };
    }

    // check if all messages have the same conversation_id
    const firstConversationId = request.messages[0].conversation_id;
    if (request.messages.some(message => message.conversation_id !== firstConversationId)) {
      return { isValid: false, error: 'all messages must have the same conversation_id' };
    }

    return { isValid: true };
  }

  /**
   * Validate search memory request
   */
  static validateSearchMemoryRequest(request: SearchMemoryRequestV2): ValidationResult {
    if (!request.config?.filters?.userId) {
      return { isValid: false, error: 'userId is required in search memory request' };
    }
    if (!request.text || typeof request.text !== 'string') {
      return { isValid: false, error: 'text is required' };
    }

    return { isValid: true };
  }

  /**
   * Validate add site message request
   */
  static validateAddSiteMessageRequest(request: AddSiteMessageRequest): ValidationResult {
    if (!request.messages || !Array.isArray(request.messages) || request.messages.length === 0) {
      return { isValid: false, error: 'messages array is required and must not be empty' };
    }
    if (!request.config?.filters?.userId) {
      return { isValid: false, error: 'config.filters.userId is required' };
    }
    return { isValid: true };
  }

  /**
   * Validate procedural memory configuration
   */
  static validateProceduralMemoryConfig(config?: MemoryConfig): ValidationResult {
    const metadata = config?.metadata as MemoryMetadata | undefined;
    if (!metadata?.workflowId || !metadata?.agentId || !metadata?.taskId) {
      return {
        isValid: false,
        error: 'workflowId, agentId, and taskId are required for procedural memory',
      };
    }
    return { isValid: true };
  }
}
