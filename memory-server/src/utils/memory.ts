import {
  Message,
  MessageRole,
  MemoryConfig,
  MemoryType,
  ExecutionStep,
  ChatAgent,
} from '@the-agent/shared';
import { VectorStoreInterface, LLMRuntimeInterface } from '../types/memory';
import { MEMORY_AGENT_CONTEXT_INSTRUCTIONS } from '../agents/prompt';

/**
 * Generate user message with context for memory processing
 */
export function generateUserMessageWithContext(messages: Message[]): string {
  const context = messages.map(formatMessage).join('\n');

  return `\
    # 🧠 Conversation Context:
    ${context}

    # 📌 Instructions:
    ${MEMORY_AGENT_CONTEXT_INSTRUCTIONS}
  `;
}

/**
 * Format individual message for context
 */
function formatMessage(msg: Message): string {
  if (msg.role === 'user' || msg.role === 'assistant') {
    const reasoning = (msg as { reasoning?: string }).reasoning;
    const content = msg.content || '';
    const reasoningText = reasoning ? `\nReasoning: ${reasoning}` : '';
    return `${msg.role}: ${content}${reasoningText}`;
  }

  if (msg.role === 'tool') {
    const toolName = (msg as { name?: string }).name || 'unknown_tool';
    const toolCallId = (msg as { tool_call_id?: string }).tool_call_id || '';
    let result = 'No result';

    // Use tool_call_result if available, otherwise fall back to content
    if (msg.tool_call_result) {
      if (msg.tool_call_result.success) {
        result = msg.tool_call_result.data ? JSON.stringify(msg.tool_call_result.data) : 'Success';
      } else {
        result = msg.tool_call_result.error || 'Failed';
      }
    } else if (msg.content) {
      try {
        const content = JSON.parse(msg.content);
        if (content.success) {
          result = content.data ? JSON.stringify(content.data) : 'Success';
        } else {
          result = 'Failed';
        }
      } catch {
        result = msg.content || 'No result';
      }
    }

    return `tool[${toolName}${toolCallId ? `:${toolCallId}` : ''}]: ${result}`;
  }

  if (msg.tool_calls && msg.tool_calls.length > 0) {
    const toolCalls = msg.tool_calls
      .map(tc => {
        const name = tc.function?.name || 'unknown';
        const args = tc.function?.arguments || '{}';
        return `${name}(${args})`;
      })
      .join(', ');
    return `tool_calls: [${toolCalls}]`;
  }

  return JSON.stringify(msg);
}

/**
 * Process memory asynchronously based on type
 */
export async function processMemoryAsync(
  messages: Message[],
  config: MemoryConfig,
  memoryAgent: ChatAgent,
  vectorStore: VectorStoreInterface,
  llmRuntime: LLMRuntimeInterface
): Promise<void> {
  try {
    // all messages are processed with semantic processing - extract knowledge
    await processSemanticMemory(messages, config, memoryAgent);
    await processProceduralMemory(messages, config, vectorStore, llmRuntime);
  } catch (error) {
    console.error('Memory processing error:', error);
  }
}

/**
 * Process semantic memory using memory agent
 */
async function processSemanticMemory(
  messages: Message[],
  config: MemoryConfig,
  memoryAgent: ChatAgent
): Promise<void> {
  const enhancedContext = generateUserMessageWithContext(messages);
  const userMessage = createUserMessage(enhancedContext, messages);

  await memoryAgent.run(userMessage, {
    toolOptions: {
      filters: config?.filters || {},
      metadata: config?.metadata || {},
    },
  });
}

/**
 * Process procedural memory by generating structured summary
 */
async function processProceduralMemory(
  messages: Message[],
  config: MemoryConfig,
  vectorStore: VectorStoreInterface,
  llmRuntime: LLMRuntimeInterface
): Promise<void> {
  try {
    const summary = await generateProceduralSummary(messages);
    const embedding = await llmRuntime.embed(summary);
    const payload = createProceduralMemoryPayload(summary, messages, config);

    await vectorStore.insert([embedding], [payload]);
  } catch (error) {
    console.error('processProceduralMemory error:', error);
    throw error;
  }
}

/**
 * Create payload for procedural memory storage
 */
function createProceduralMemoryPayload(summary: string, messages: Message[], config: MemoryConfig) {
  const now = new Date().toISOString();

  return {
    text: summary,
    metadata: {
      ...config?.metadata,
      memoryType: 'procedural' as MemoryType,
      data: summary,
      created_at: now,
      updated_at: now,
    },
  };
}

/**
 * Process site message asynchronously
 */
export async function processSiteMessageAsync(
  messages: Message[],
  config: MemoryConfig,
  memoryAgent: ChatAgent
): Promise<void> {
  try {
    const enhancedContext = generateUserMessageWithContext(messages);
    const userMessage = createUserMessage(enhancedContext, messages);

    await memoryAgent.run(userMessage, {
      toolOptions: {
        filters: config.filters,
        metadata: config.metadata,
      },
    });
  } catch (error) {
    console.error('Site message processing error:', error);
  }
}

/**
 * Create user message for memory processing
 */
function createUserMessage(content: string, messages: Message[]): Message {
  return {
    id: Date.now(),
    conversation_id: messages[0]?.conversation_id,
    role: 'user' as MessageRole,
    content,
  };
}

/**
 * Generate structured summary from execution steps
 */
async function generateProceduralSummary(messages: Message[]): Promise<string> {
  const taskGoal = extractTaskGoal(messages);
  console.log('taskGoal', taskGoal);
  const progressStatus = calculateProgress(messages);
  const steps = extractExecutionSteps(messages);

  return formatProceduralSummary(taskGoal, progressStatus, steps);
}

/**
 * Format procedural memory summary
 */
function formatProceduralSummary(
  taskGoal: string,
  progressStatus: string,
  steps: Omit<ExecutionStep, 'stepNumber' | 'timestamp'>[]
): string {
  const stepsSection = steps.map((step, index) => formatExecutionStep(step, index + 1)).join('\n');

  return `
## Summary of the agent's execution history
**Task Objective**: ${taskGoal}
**Progress Status**: ${progressStatus}
${stepsSection}
  `.trim();
}

/**
 * Format individual execution step
 */
function formatExecutionStep(
  step: Omit<ExecutionStep, 'stepNumber' | 'timestamp'>,
  stepNumber: number
): string {
  const optionalFields = [
    step.keyFindings && `**Key Findings**: ${step.keyFindings}`,
    step.navigationHistory && `**Navigation History**: ${step.navigationHistory}`,
    step.errors && `**Errors & Challenges**: ${step.errors}`,
  ]
    .filter(Boolean)
    .join('\n   ');

  const optionalSection = optionalFields ? `${optionalFields}\n   ` : '';

  return `
${stepNumber}. **Agent Action**: ${step.agentAction}
   **Action Result**: ${step.actionResult}
   ${optionalSection}**Current Context**: ${step.currentContext}
`;
}

/**
 * Extract task goal from messages
 */
function extractTaskGoal(messages: Message[]): string {
  const userMessages = messages.filter(msg => msg.role === 'user');
  return userMessages[0]?.content || 'Unknown task';
}

/**
 * Calculate progress status
 */
function calculateProgress(messages: Message[]): string {
  const totalSteps = messages.filter(msg => msg.role === 'assistant' || msg.role === 'tool').length;
  const completedSteps = messages.filter(msg => msg.role === 'assistant' && msg.content).length;
  const percentage = totalSteps > 0 ? Math.round((completedSteps / totalSteps) * 100) : 0;
  return `${percentage}% complete (${completedSteps}/${totalSteps} steps)`;
}

/**
 * Extract execution steps from messages
 */
function extractExecutionSteps(
  messages: Message[]
): Omit<ExecutionStep, 'stepNumber' | 'timestamp'>[] {
  const steps: Omit<ExecutionStep, 'stepNumber' | 'timestamp'>[] = [];
  const currentStep = createEmptyStep();

  for (const msg of messages) {
    if (msg.role === 'tool') {
      const toolResult = JSON.parse(msg.content || '{}');
      currentStep.agentAction = `${msg.name}`;
      currentStep.actionResult = toolResult.success ? toolResult.data : toolResult.error;
      currentStep.currentContext = toolResult.success ? 'Success' : 'Failed';
    }
  }

  if (currentStep.agentAction) {
    steps.push(currentStep);
  }

  return steps;
}

/**
 * Create empty execution step
 */
function createEmptyStep(): Omit<ExecutionStep, 'stepNumber' | 'timestamp'> {
  return {
    agentAction: '',
    actionResult: '',
    currentContext: '',
  };
}

/**
 * Create assistant execution step
 */
function createAssistantStep(
  content: string | null
): Omit<ExecutionStep, 'stepNumber' | 'timestamp'> {
  return {
    agentAction: content || 'Unknown action',
    actionResult: '',
    currentContext: 'Processing...',
  };
}
