import { Request, Response } from 'express';
import {
  AddMemoryRequest,
  SearchMemoryResponse,
  AddSiteMessageRequest,
  SearchMemoryRequestV2,
  ChatAgent,
} from '@the-agent/shared';
import { VectorStoreInterface, LLMRuntimeInterface } from '../../types/memory';
import { MemoryValidator } from '../../utils/validator';
import { processMemoryAsync, processSiteMessageAsync } from '../../utils/memory';

export class MemoryService {
  constructor(
    private memoryAgent: ChatAgent,
    private vectorStore: VectorStoreInterface,
    private llmRuntime: LLMRuntimeInterface
  ) {}

  /**
   * Add memory with preprocessing and validation
   * @param req Request containing messages and config
   * @param res Response object
   */
  async addMemory(req: Request, res: Response): Promise<void> {
    try {
      const requestBody = req.body as AddMemoryRequest;
      const validationResult = MemoryValidator.validateAddMemoryRequest(requestBody);

      if (!validationResult.isValid) {
        res.status(400).json({ error: validationResult.error || 'Validation failed' });
        return;
      }

      const { messages, config } = requestBody;

      res.status(201).json({ results: [], relations: [] });

      processMemoryAsync(
        messages,
        config || {},
        this.memoryAgent,
        this.vectorStore,
        this.llmRuntime
      );
    } catch (error) {
      console.error('addMemory error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Search memories with validation and filtering
   * @param req Request containing search parameters
   * @param res Response object
   */
  async searchMemories(req: Request, res: Response): Promise<void> {
    try {
      const requestBody = req.body as SearchMemoryRequestV2;
      const validationResult = MemoryValidator.validateSearchMemoryRequest(requestBody);

      if (!validationResult.isValid) {
        res.status(400).json({ error: validationResult.error || 'Search validation failed' });
        return;
      }

      const { text, config } = requestBody;

      const queryEmbedding = await this.llmRuntime.embed(text);

      const searchFilters = {
        ...(config?.filters ?? {}),
      };

      const memories = await this.vectorStore.search(queryEmbedding, config?.limit, searchFilters);

      const response: SearchMemoryResponse = {
        results: memories.map(mem => ({
          id: mem.id,
          memory: mem.memory,
          score: mem.score,
          metadata: mem.metadata,
        })),
      };

      res.status(200).json(response);
    } catch (error) {
      console.error('searchMemories error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }

  /**
   * Add site message with validation
   * @param req Request containing site message data
   * @param res Response object
   */
  async addSiteMessage(req: Request, res: Response): Promise<void> {
    try {
      const requestBody = req.body as AddSiteMessageRequest;
      const validationResult = MemoryValidator.validateAddSiteMessageRequest(requestBody);

      if (!validationResult.isValid) {
        res.status(400).json({ error: validationResult.error || 'Site message validation failed' });
        return;
      }

      const { messages, config } = requestBody;

      res.status(201).json({ results: [], relations: [] });

      processSiteMessageAsync(messages, config, this.memoryAgent);
    } catch (error) {
      console.error('addSiteMessage error:', error);
      res.status(500).json({ error: 'Internal server error' });
    }
  }
}
